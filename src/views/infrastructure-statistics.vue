<template>
  <div class="infrastructure-statistics-container">
    <!-- 顶部标题和年份选择器 -->
    <div class="header-section">
      <div class="title-wrapper">
        <h2 class="page-title">数据分析</h2>
        <div class="year-selector">
          <span class="year-label">选择年份：</span>
          <el-select v-model="selectedYear" placeholder="请选择年份" class="year-select">
            <el-option v-for="year in years" :key="year" :label="year + '年'" :value="year" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 基建项目性质统计图表 -->
    <div class="chart-section">
      <div class="chart-title-wrapper">
        <h3 class="chart-title">基建项目性质统计</h3>
      </div>
      <div class="chart-container">
        <LineChart :chart-data="chartData" height="400px" />
      </div>
    </div>

    <!-- 数据卡片部分 -->
    <el-row :gutter="20" class="data-cards-section">
      <el-col :xs="12" :sm="12" :md="12" :lg="6" class="data-card-col">
        <div class="data-card">
          <div class="data-card-content">
            <div class="data-card-title">总建筑面积（m²）</div>
            <div class="data-card-value">{{ totalArea }}</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="6" class="data-card-col">
        <div class="data-card">
          <div class="data-card-content">
            <div class="data-card-title">总项目用地（m²）</div>
            <div class="data-card-value">{{ totalLandArea }}</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="6" class="data-card-col">
        <div class="data-card">
          <div class="data-card-content">
            <div class="data-card-title">基建项目总数</div>
            <div class="data-card-value">{{ totalProjects }}</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="6" class="data-card-col">
        <div class="data-card">
          <div class="data-card-content">
            <div class="data-card-title">总投资（万元）</div>
            <div class="data-card-value">{{ totalInvestment }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 项目总金额排名表格 -->
    <div class="table-section">
      <div class="table-title-wrapper">
        <h3 class="table-title">基建项目总金额排名TOP5</h3>
      </div>
      <el-table :data="projectRankData" style="width: 100%" class="ranking-table">
        <el-table-column prop="rank" label="序号" width="80" align="center" />
        <el-table-column prop="name" label="名称" min-width="300" />
        <el-table-column prop="investment" label="总投资（万元）" width="150" align="center">
          <template slot-scope="scope">
            <span class="investment-value">{{ scope.row.investment }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="nature" label="性质" width="120" align="center" />
      </el-table>
    </div>
  </div>
</template>

<script>
import LineChart from './dashboard/LineChart.vue'

export default {
  name: 'InfrastructureStatistics',
  components: {
    LineChart
  },
  data() {
    return {
      years: [2020, 2021, 2022, 2023, 2024, 2025],
      selectedYear: 2025,
      totalArea: '36000',
      totalLandArea: '1000',
      totalProjects: '1500',
      totalInvestment: '24',
      chartData: {
        expectedData: [
          { name: '立项', value: 12 },
          { name: '在建', value: 8 },
          { name: '第一批急迫', value: 16 },
          { name: '第二批', value: 10 },
          { name: '第三批', value: 6 }
        ],
        actualData: [
          { name: '立项', value: 12 },
          { name: '在建', value: 8 },
          { name: '第一批急迫', value: 16 },
          { name: '第二批', value: 10 },
          { name: '第三批', value: 6 }
        ]
      },
      projectRankData: [
        {
          rank: 1,
          name: '椒江区2024年教育领域为民办实事工程项目',
          investment: '300',
          nature: '第二批'
        },
        {
          rank: 2,
          name: '椒江区2024年教育领域为民办实事工程项目',
          investment: '180',
          nature: '第二批'
        },
        {
          rank: 3,
          name: '椒江区2024年教育领域为民办实事工程项目',
          investment: '100',
          nature: '第一批急迫'
        },
        {
          rank: 4,
          name: '椒江区2024年教育领域为民办实事工程项目',
          investment: '90',
          nature: '第三批'
        },
        {
          rank: 5,
          name: '椒江区2024年教育领域为民办实事工程项目',
          investment: '80',
          nature: '立项'
        }
      ]
    }
  },
  watch: {
    selectedYear: {
      handler(newYear) {
        // 当选择的年份变化时，可以在这里加载对应年份的数据
        this.loadYearData(newYear)
      }
    }
  },
  mounted() {
    // 页面加载时初始化数据
    this.loadYearData(this.selectedYear)
  },
  methods: {
    loadYearData(year) {
      // 这里可以根据实际需求从API获取对应年份的数据
      // 目前使用的是模拟数据
      console.log('加载', year, '年的数据')
      // 模拟数据加载延迟
      setTimeout(() => {
        // 更新图表数据
        this.updateChartData(year)
        // 更新统计数据
        this.updateStatisticalData(year)
        // 更新排名数据
        this.updateRankData(year)
      }, 300)
    },
    updateChartData(year) {
      // 根据年份更新图表数据
      // 这里使用的是模拟数据
      this.chartData = {
        expectedData: [
          { name: '立项', value: 12 },
          { name: '在建', value: 8 },
          { name: '第一批急迫', value: 16 },
          { name: '第二批', value: 10 },
          { name: '第三批', value: 6 }
        ],
        actualData: [
          { name: '立项', value: 12 },
          { name: '在建', value: 8 },
          { name: '第一批急迫', value: 16 },
          { name: '第二批', value: 10 },
          { name: '第三批', value: 6 }
        ]
      }
    },
    updateStatisticalData(year) {
      // 根据年份更新统计数据
      // 这里使用的是模拟数据
      this.totalArea = '36000'
      this.totalLandArea = '1000'
      this.totalProjects = '1500'
      this.totalInvestment = '24'
    },
    updateRankData(year) {
      // 根据年份更新排名数据
      // 这里使用的是模拟数据
      // 实际项目中可以从API获取数据后更新
    }
  }
}
</script>

<style lang="scss" scoped>
.infrastructure-statistics-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header-section {
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.year-selector {
  display: flex;
  align-items: center;
}

.year-label {
  margin-right: 8px;
  font-size: 14px;
  color: #606266;
}

.year-select {
  width: 120px;
}

.chart-section {
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.chart-title-wrapper {
  margin-bottom: 20px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  width: 100%;
}

.data-cards-section {
  margin-bottom: 20px;
}

.data-card-col {
  margin-bottom: 20px;
}

.data-card {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  height: 100%;
}

.data-card-content {
  text-align: center;
}

.data-card-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.data-card-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.table-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.table-title-wrapper {
  margin-bottom: 20px;
}

.table-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.ranking-table {
  border: 1px solid #ebeef5;
}

.ranking-table th {
  background-color: #fafafa;
  font-weight: 600;
  color: #303133;
}

.investment-value {
  color: #409eff;
  font-weight: 600;
}

/* 响应式样式调整 */
@media (max-width: 768px) {
  .title-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }

  .year-selector {
    margin-top: 10px;
  }
}

</style>
