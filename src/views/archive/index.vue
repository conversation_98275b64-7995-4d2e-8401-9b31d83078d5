<template>
  <div class="archive-container">
    <div class="search-section">
      <el-form :inline="true" class="search-form">
        <el-form-item label="年份：">
          <el-select v-model="year" placeholder="请选择年份" class="select-width">
            <el-option label="2022" value="2022" />
            <el-option label="2021" value="2021" />
            <el-option label="2020" value="2020" />
          </el-select>
        </el-form-item>

        <el-form-item label="创建日期：">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="date-picker-width"
          />
        </el-form-item>

        <el-form-item label="标题：">
          <el-input
            v-model="title"
            placeholder="全文检索、关键词检索、分类检索"
            class="input-width"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>

        <el-form-item>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="filter-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="全部" name="all" />
            <el-tab-pane label="基建" name="infrastructure" />
            <el-tab-pane label="会计" name="accounting" />
            <el-tab-pane label="人事" name="hr" />
            <el-tab-pane label="照片" name="photos" />
            <el-tab-pane label="录音" name="audio" />
            <el-tab-pane label="录像" name="video" />
            <el-tab-pane label="业务数据" name="business" />
            <el-tab-pane label="公务电子邮箱" name="email" />
            <el-tab-pane label="网页信息" name="web" />
          </el-tabs>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="retention-period">
        <el-col :span="24">
          <el-button
            v-for="period in retentionPeriods"
            :key="period.value"
            :type="selectedPeriod === period.value ? 'primary' : 'default'"
            class="period-button"
            @click="selectRetentionPeriod(period.value)"
          >
            {{ period.label }}
          </el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Archive',
  data() {
    return {
      year: '',
      dateRange: '',
      title: '',
      activeTab: 'all',
      selectedPeriod: '',
      retentionPeriods: [
        { label: '全部', value: 'all' },
        { label: '永久', value: 'permanent' },
        { label: '30年', value: '30years' },
        { label: '10年', value: '10years' }
      ]
    }
  },
  methods: {
    search() {
      // 实现查询功能
      console.log('查询条件：', {
        year: this.year,
        dateRange: this.dateRange,
        title: this.title,
        category: this.activeTab,
        retentionPeriod: this.selectedPeriod
      })
      // 这里应该调用API获取数据
    },
    reset() {
      // 重置表单
      this.year = ''
      this.dateRange = ''
      this.title = ''
      this.activeTab = 'all'
      this.selectedPeriod = ''
    },
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.search()
    },
    selectRetentionPeriod(period) {
      this.selectedPeriod = period
      this.search()
    }
  }
}
</script>

<style scoped>
.archive-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.search-section {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-form .select-width {
  width: 120px;
}

.search-form .date-picker-width {
  width: 300px;
}

.search-form .input-width {
  width: 300px;
}

.filter-section {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
}

.retention-period {
  margin-top: 15px;
}

.period-button {
  margin-right: 10px;
}
</style>
