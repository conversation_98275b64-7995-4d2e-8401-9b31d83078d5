import Vue from 'vue'
import Router from 'vue-router'
import Layout from '../layout/index'

Vue.use(Router)

export const constantRouterMap = [
  { path: '/login',
    meta: { title: '登录', noCache: true },
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },

  {
    path: '/404',
    component: (resolve) => require(['@/views/features/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/features/401'], resolve),
    hidden: true
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: (resolve) => require(['@/views/features/redirect'], resolve)
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: (resolve) => require(['@/views/home'], resolve),
        name: 'Dash<PERSON>',
        meta: { title: '首页', icon: 'index', affix: true, noCache: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'center',
        component: (resolve) => require(['@/views/system/user/center'], resolve),
        name: '个人中心',
        meta: { title: '个人中心' }
      }
    ]
  },
  {
    path: '/archive',
    component: Layout,
    redirect: '/archive',
    children: [
      {
        path: 'archive',
        component: (resolve) => require(['@/views/archive'], resolve),
        name: 'archive',
        meta: { title: '归档库', icon: 'index', affix: true, noCache: true }
      }
    ]
  },
  {
    path: '/manual',
    component: Layout,
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/manual'], resolve),
        name: '人工著录',
        meta: { title: '人工著录', icon: 'index', affix: true, noCache: true }
      }
    ]
  },
  {
    path: '/infrastructure-statistics',
    component: Layout,
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/infrastructure-statistics'], resolve),
        name: '数据分析',
        meta: { title: '数据分析', icon: 'index', affix: true, noCache: true }
      }
    ]
  }
]

export default new Router({
  // mode: 'hash',
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})
